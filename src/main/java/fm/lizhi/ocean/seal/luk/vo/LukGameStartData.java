package fm.lizhi.ocean.seal.luk.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * LUK游戏开始事件数据
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class LukGameStartData {

    /**
     * 游戏开始时间（秒级时间戳）
     */
    @JsonProperty("start_unix_sec")
    private Integer startUnixSec;

    /**
     * 参与游戏的玩家 ID 列表
     */
    @JsonProperty("user_ids")
    private List<String> userIds;

    /**
     * 操作玩家 ID（可选）
     */
    @JsonProperty("op_user_id")
    private String opUserId;
}
