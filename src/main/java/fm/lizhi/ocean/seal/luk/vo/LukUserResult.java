package fm.lizhi.ocean.seal.luk.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * LUK用户游戏结果
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class LukUserResult {

    /**
     * 玩家得分（可选）
     */
    private Integer score;

    /**
     * 玩家排名（可选）
     */
    private Integer rank;

    /**
     * 玩家状态（0 平局，1 胜利，2 失败）（可选）
     */
    private Integer status;

    /**
     * 是否逃跑（可选）
     */
    private Boolean escape;

    /**
     * 是否托管（可选）
     */
    private Boolean trust;
}
