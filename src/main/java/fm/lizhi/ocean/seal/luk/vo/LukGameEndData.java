package fm.lizhi.ocean.seal.luk.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * LUK游戏结束事件数据
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class LukGameEndData {

    /**
     * 游戏开始时间（秒级时间戳）
     */
    @JsonProperty("start_unix_sec")
    private Integer startUnixSec;

    /**
     * 游戏结束时间（秒级时间戳）
     */
    @JsonProperty("end_unix_sec")
    private Integer endUnixSec;

    /**
     * 玩家游戏结果（玩家ID => 结果）
     */
    @JsonProperty("user_results")
    private Map<String, LukUserResult> userResults;

    /**
     * 游戏结束类型（默认可为 0）
     * 0: 正常游戏结束
     * 1: 仅剩机器人导致结束
     * 2: 全部玩家离开导致结束
     * 3: 管理员强制结束
     * 4: 异常结束（流程已经无法继续，异常情况拦截导致结束）
     */
    @JsonProperty("end_type")
    private Integer endType;

    /**
     * 结束扩展信息（可选）
     */
    @JsonProperty("end_ext")
    private String endExt;

    /**
     * 这一局消耗货币数（可选）
     */
    @JsonProperty("cost_coins")
    private Integer costCoins;

    /**
     * 操作玩家 ID（可选）
     */
    @JsonProperty("op_user_id")
    private String opUserId;
}
