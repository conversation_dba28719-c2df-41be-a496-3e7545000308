package fm.lizhi.ocean.seal.luk.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * LUK游戏通知事件回调请求
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class LukNotifyEventReq {

    /**
     * APP ID
     */
    @JsonProperty("app_id")
    private Integer appId;

    /**
     * 游戏 ID
     */
    @JsonProperty("game_id")
    private Integer gameId;

    /**
     * 房间 ID
     */
    @JsonProperty("room_id")
    private String roomId;

    /**
     * 游戏局 ID
     */
    @JsonProperty("round_id")
    private String roundId;

    /**
     * 签名
     */
    private String sign;

    /**
     * 时间戳
     */
    private Integer timestamp;

    /**
     * 事件类型（1=开始游戏事件，2=游戏结束事件）
     */
    private Integer type;

    /**
     * JSON 字符串的事件详情
     */
    private String data;

    /**
     * 检查是否为游戏开始事件
     * @return true if game start event
     */
    public boolean isGameStartEvent() {
        return type != null && type == 1;
    }

    /**
     * 检查是否为游戏结束事件
     * @return true if game end event
     */
    public boolean isGameEndEvent() {
        return type != null && type == 2;
    }
}
