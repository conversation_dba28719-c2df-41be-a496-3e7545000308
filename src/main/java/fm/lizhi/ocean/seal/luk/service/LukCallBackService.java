package fm.lizhi.ocean.seal.luk.service;

import com.alibaba.fastjson.JSON;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.RetCodeEnum;
import fm.lizhi.ocean.seal.api.GameAuthService;
import fm.lizhi.ocean.seal.api.GameReportService;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto;
import fm.lizhi.ocean.seal.service.GameAppConfigService;
import fm.lizhi.ocean.seal.luk.bo.LukGameData;
import fm.lizhi.ocean.seal.luk.vo.*;
import io.github.cfgametech.sign.SignUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * LUK回调服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LukCallBackService {

    @Resource
    private GameAuthService gameAuthService;

    @Resource
    private GameAppConfigService gameAppConfigService;

    @Resource
    private GameReportService gameReportService;

    @Autowired
    private LzConfig lzConfig;

    /**
     * 刷新用户令牌
     *
     * @param appId       APP ID
     * @param appId     应用ID
     * @return 刷新令牌响应
     */
    public LukBaseResp<GetChannelTokenResp> getChannelToken(String appId, GetChannelTokenReq reqParam) {

        LukBaseResp<GetChannelTokenResp> baseResp = new LukBaseResp<>();
        try {
            //验签
            boolean signSuccess = verifySign(appId, reqParam, reqParam.getSign());
            if (!signSuccess) {
                log.warn("luck回调验签失败，找不到luck channel配置，appId: {}", appId);
                baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
                return baseResp;
            }

            Result<GameAuthServiceProto.ResponseGetServerToken> ssTokenResult = gameAuthService.getServerToken(reqParam.getToken(), GameChannel.LUK, appId);
            if (ssTokenResult.rCode() != 0 || ssTokenResult.target() == null
                    || ssTokenResult.target().getToken().getErrorCode() != 0) {
                baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
                return baseResp;
            }

            GameAuthServiceProto.ServerToken sudSSToken = ssTokenResult.target().getToken();

            // 构建响应数据
            GetChannelTokenResp respData = GetChannelTokenResp.builder()
                    .token(sudSSToken.getToken())
                    .left_time(Math.max(sudSSToken.getExpireDate(), 0)) // 确保不返回负数
                    .build();

            baseResp.setRetCode(RetCodeEnum.SUCCESS);
            baseResp.setData(respData);

            log.info("获取用户令牌成功, appId:{}, cUid:{}, , leftTime:{}", appId, reqParam.getUid(), sudSSToken.getExpireDate());

        } catch (Exception e) {
            log.error("获取用户令牌异常, appId:{}, cUid:{}", appId, reqParam.getUid(), e);
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
        }

        return baseResp;
    }

    /**
     * 刷新用户令牌
     *
     * @param appId    应用ID
     * @param reqParam 请求参数
     * @return 刷新令牌响应
     */
    public LukBaseResp<RefreshChannelTokenResp> refreshChannelToken(String appId, RefreshChannelTokenReq reqParam) {
        LukBaseResp<RefreshChannelTokenResp> baseResp = new LukBaseResp<>();

        try {
            //验签
            boolean signSuccess = verifySign(appId, reqParam, reqParam.getSign());
            if (!signSuccess) {
                log.warn("luck回调刷新token验签失败，找不到luck channel配置，appId: {}", appId);
                baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
                return baseResp;
            }

            Result<GameAuthServiceProto.ResponseUpdateServerToken> updateSSTokenResult = gameAuthService.updateServerToken(reqParam.getToken(), GameChannel.LUK, appId);
            if (updateSSTokenResult.rCode() != 0 || updateSSTokenResult.target() == null
                    || updateSSTokenResult.target().getToken().getErrorCode() != 0) {
                baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
                return baseResp;
            }
            GameAuthServiceProto.ServerToken sudSSToken = updateSSTokenResult.target().getToken();

            RefreshChannelTokenResp refreshChannelTokenResp = RefreshChannelTokenResp.builder()
                    .token(sudSSToken.getToken())
                    .left_time(Math.max(sudSSToken.getExpireDate(), 0)).build();
            log.info("刷新用户令牌成功, appId:{}, cUid:{}, , leftTime:{}", appId, reqParam.getUid(), sudSSToken.getExpireDate());
            baseResp.setRetCode(RetCodeEnum.SUCCESS);
            baseResp.setData(refreshChannelTokenResp);

        } catch (Exception e) {
            log.error("刷新用户令牌异常, appId:{}, cUid:{}", appId, reqParam.getUid(), e);
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
        }

        return baseResp;
    }

    private boolean verifySign(String appId, Object reqParam, String sign) {

        if (!lzConfig.isLuckCheckCallbackSign()) {
            log.info("luck回调不验签，请注意");
            return true;
        }
        GameAppServiceProto.GameAppInfo gameAppInfo = gameAppConfigService.getGameAppInfo(appId);
        Optional<GameAppServiceProto.ChannelInfo> channelInfo = gameAppInfo.getChannelInfosList()
                .stream()
                .filter(channel -> Objects.equals(channel.getChannel(), GameChannel.LUK))
                .findAny();

        String channelSecret = channelInfo.map(GameAppServiceProto.ChannelInfo::getChannelAppSecret).orElse(null);
        if (channelSecret == null) {
            log.warn("luck回调获取token验签失败，找不到luck channel配置，appId: {}", appId);
            return false;
        }
        try {
            String signResult = SignUtils.signature(channelSecret, reqParam);
            if (!Objects.equals(signResult, sign)) {
                log.warn("luck回调验签失败，appId: {}", appId);
                return false;
            }
            return true;
        } catch (IllegalAccessException e) {
            log.error("luck回调验签失败，appId: {}", appId, e);
            return false;
        }
    }

    /**
     * 游戏通知事件回调
     *
     * @param appId    应用ID
     * @param reqParam 请求参数
     * @return 响应结果
     */
    public LukBaseResp<Void> notifyEvent(String appId, LukNotifyEventReq reqParam) {
        LukBaseResp<Void> baseResp = new LukBaseResp<>();

        try {
            // 验签
            boolean signSuccess = verifySign(appId, reqParam, reqParam.getSign());
            if (!signSuccess) {
                log.warn("luk游戏通知事件回调验签失败，appId: {}", appId);
                baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
                return baseResp;
            }

            // 解析事件数据
            LukGameData lukGameData = LukGameData.builder()
                    .gameId(reqParam.getGameId())
                    .roomId(reqParam.getRoomId())
                    .roundId(reqParam.getRoundId())
                    .build();

            Result<Void> result;

            if (reqParam.isGameStartEvent()) {
                // 解析游戏开始事件数据
                LukGameStartData gameStartData = JSON.parseObject(reqParam.getData(), LukGameStartData.class);
                lukGameData.setGameStartData(gameStartData);

                // 调用游戏开始报告
                result = gameReportService.gameStartReport(lukGameData.buildGameStartResult(appId, GameChannel.LUK));
                log.info("luk游戏开始事件处理完成, appId:{}, gameId:{}, roundId:{}", appId, reqParam.getGameId(), reqParam.getRoundId());

            } else if (reqParam.isGameEndEvent()) {
                // 解析游戏结束事件数据
                LukGameEndData gameEndData = JSON.parseObject(reqParam.getData(), LukGameEndData.class);
                lukGameData.setGameEndData(gameEndData);

                // 调用游戏结束报告
                result = gameReportService.gameSettleReport(lukGameData.buildGameSettleResult(appId, GameChannel.LUK));
                log.info("luk游戏结束事件处理完成, appId:{}, gameId:{}, roundId:{}", appId, reqParam.getGameId(), reqParam.getRoundId());

            } else {
                log.warn("luk游戏通知事件类型不支持,跳过. type:{}, appId:{}", reqParam.getType(), appId);
                baseResp.setRetCode(RetCodeEnum.SUCCESS);
                return baseResp;
            }

            if (result.rCode() != 0) {
                log.error("luk游戏通知事件处理失败, rCode: {}, reqParam:{}", result.rCode(), reqParam);
                baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
                return baseResp;
            }

            baseResp.setRetCode(RetCodeEnum.SUCCESS);
            log.info("luk游戏通知事件处理成功, reqParam:{}", reqParam);

        } catch (Exception e) {
            log.error("luk游戏通知事件处理异常, appId:{}, reqParam:{}", appId, reqParam, e);
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
        }

        return baseResp;
    }
}
