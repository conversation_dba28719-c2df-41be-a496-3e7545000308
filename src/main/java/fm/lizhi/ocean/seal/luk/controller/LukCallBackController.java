package fm.lizhi.ocean.seal.luk.controller;

import com.alibaba.fastjson.JSON;
import fm.lizhi.ocean.seal.luk.service.LukCallBackService;
import fm.lizhi.ocean.seal.luk.vo.GetChannelTokenReq;
import fm.lizhi.ocean.seal.luk.vo.LukNotifyEventReq;
import fm.lizhi.ocean.seal.luk.vo.RefreshChannelTokenReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * LUK回调controller
 *
 * <AUTHOR>
 * @create 2025/07/02 15:50
 */
@RestController
@Slf4j
@RequestMapping("luk")
public class LukCallBackController {

    @Resource
    private LukCallBackService lukCallBackService;

    /**
     * 刷新用户令牌
     * 调用方：游戏服务
     *
     * @param appId    应用ID
     * @param reqParam 请求参数
     * @return 刷新令牌响应
     */
    @PostMapping("/{appId}/get_channel_token")
    public Object getChannelToken(@PathVariable("appId") String appId, @RequestBody GetChannelTokenReq reqParam) {
        log.info("刷新用户令牌请求参数：{}", JSON.toJSONString(reqParam));
        return lukCallBackService.getChannelToken(appId, reqParam);
    }

    /**
     * 刷新用户令牌
     * 调用方：游戏服务
     *
     * @param appId    应用ID
     * @param reqParam 请求参数
     * @return 刷新令牌响应
     */
    @PostMapping("/{appId}/refresh_channel_token")
    public Object refreshChannelToken(@PathVariable("appId") String appId, @RequestBody RefreshChannelTokenReq reqParam) {
        log.info("刷新用户令牌请求参数：{}", JSON.toJSONString(reqParam));
        return lukCallBackService.refreshChannelToken(appId, reqParam);
    }

    /**
     * 游戏通知事件回调
     * 调用方：游戏服务
     *
     * @param appId    应用ID
     * @param reqParam 请求参数
     * @return 响应结果
     */
    @PostMapping("/{appId}/notify_event")
    public Object notifyEvent(@PathVariable("appId") String appId, @RequestBody LukNotifyEventReq reqParam) {
        log.info("游戏通知事件回调请求参数：{}", JSON.toJSONString(reqParam));
        return lukCallBackService.notifyEvent(appId, reqParam);
    }
}
