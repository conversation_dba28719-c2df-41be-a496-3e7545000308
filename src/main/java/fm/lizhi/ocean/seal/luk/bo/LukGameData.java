package fm.lizhi.ocean.seal.luk.bo;

import com.alibaba.fastjson.JSON;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import fm.lizhi.ocean.seal.luk.vo.LukGameEndData;
import fm.lizhi.ocean.seal.luk.vo.LukGameStartData;
import fm.lizhi.ocean.seal.luk.vo.LukUserResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * LUK游戏数据转换类
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class LukGameData {

    /**
     * 游戏 ID
     */
    private Integer gameId;

    /**
     * 房间 ID
     */
    private String roomId;

    /**
     * 游戏局 ID
     */
    private String roundId;

    /**
     * 游戏开始数据
     */
    private LukGameStartData gameStartData;

    /**
     * 游戏结束数据
     */
    private LukGameEndData gameEndData;

    /**
     * 构建游戏开始结果
     *
     * @param appId   应用ID
     * @param channel 渠道
     * @return GameStartResult
     */
    public GameReportServiceProto.GameStartResult buildGameStartResult(String appId, String channel) {
        GameReportServiceProto.GameStartResult.Builder builder = GameReportServiceProto.GameStartResult.newBuilder()
                .setAppId(appId)
                .setGameId(gameId.longValue())
                .setChannelGameId(String.valueOf(gameId))
                .setGameRoundId(roundId)
                .setRoomId(roomId)
                .setChannel(channel)
                // todo 这样要传啥？
                .setEnv(-1)
                .setReportGameInfoExtras("")
                .setRawResult(JSON.toJSONString(this));

        // 设置游戏开始时间
        if (gameStartData != null && gameStartData.getStartUnixSec() != null) {
            builder.setGameStartAtTime(gameStartData.getStartUnixSec() * 1000L);
        } else {
            builder.setGameStartAtTime(System.currentTimeMillis());
        }

        // 添加玩家信息
        if (gameStartData != null && gameStartData.getUserIds() != null) {
            for (String userId : gameStartData.getUserIds()) {
                GameReportServiceProto.GamePlayerResult playerResult = GameReportServiceProto.GamePlayerResult.newBuilder()
                        .setUid(userId)
                        .setRealUser(true)
                        .build();
                builder.addGamePlayers(playerResult);
            }
        }

        return builder.build();
    }

    /**
     * 构建游戏结束结果
     *
     * @param appId   应用ID
     * @param channel 渠道
     * @return GameSettleResult
     */
    public GameReportServiceProto.GameSettleResult buildGameSettleResult(String appId, String channel) {
        GameReportServiceProto.GameSettleResult.Builder builder = GameReportServiceProto.GameSettleResult.newBuilder()
                .setAppId(appId)
                .setGameId(gameId.longValue())
                .setChannelGameId(String.valueOf(gameId))
                .setGameRoundId(roundId)
                .setRoomId(roomId)
                .setChannel(channel)
                // todo 这样要传啥？
                .setEnv(-1)
                .setReportGameInfoExtras("")
                .setRawResult(JSON.toJSONString(this));

        if (gameEndData != null) {
            // 设置游戏时间
            if (gameEndData.getStartUnixSec() != null) {
                builder.setGameStartAtTime(gameEndData.getStartUnixSec() * 1000L);
            }
            if (gameEndData.getEndUnixSec() != null) {
                builder.setGameEndAtTime(gameEndData.getEndUnixSec() * 1000L);
                
                // 计算游戏时长
                if (gameEndData.getStartUnixSec() != null) {
                    int duration = gameEndData.getEndUnixSec() - gameEndData.getStartUnixSec();
                    builder.setGameDuration(duration);
                }
            }

            // 添加玩家结果
            if (gameEndData.getUserResults() != null) {
                for (Map.Entry<String, LukUserResult> entry : gameEndData.getUserResults().entrySet()) {
                    String userId = entry.getKey();
                    LukUserResult userResult = entry.getValue();
                    
                    GameReportServiceProto.GamePlayerSettleResult.Builder playerBuilder = 
                            GameReportServiceProto.GamePlayerSettleResult.newBuilder()
                                    .setUid(userId)
                                    .setRealUser(true);

                    if (userResult != null) {
                        if (userResult.getScore() != null) {
                            playerBuilder.setScore(userResult.getScore());
                        }
                        if (userResult.getRank() != null) {
                            playerBuilder.setRank(userResult.getRank());
                        }
                        if (userResult.getStatus() != null) {
                            playerBuilder.setIsWin(userResult.getStatus());
                        }
                        if (userResult.getEscape() != null) {
                            playerBuilder.setEscaped(userResult.getEscape());
                        }
                        // trust字段在GamePlayerSettleResult中没有对应字段，暂时忽略
                    }

                    builder.addGamePlayers(playerBuilder.build());
                }
            }
        }

        return builder.build();
    }
}
